<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <br>
    <button onclick="downloadIcons()">Download Icons</button>

    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');

            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3b82f6');
            gradient.addColorStop(1, '#1e40af');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);

            // Add border radius effect
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.1);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';

            // Dollar sign with shadow
            ctx.shadowColor = 'rgba(0,0,0,0.3)';
            ctx.shadowBlur = size * 0.02;
            ctx.shadowOffsetY = size * 0.01;

            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${size * 0.5}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('$', size / 2, size / 2);

            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetY = 0;
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadIcons() {
            const canvas192 = document.getElementById('canvas192');
            const canvas512 = document.getElementById('canvas512');
            
            createIcon(canvas192, 192);
            createIcon(canvas512, 512);
            
            setTimeout(() => {
                downloadCanvas(canvas192, 'icon-192.png');
                setTimeout(() => {
                    downloadCanvas(canvas512, 'icon-512.png');
                }, 500);
            }, 100);
        }

        // Generate icons on load
        window.onload = function() {
            createIcon(document.getElementById('canvas192'), 192);
            createIcon(document.getElementById('canvas512'), 512);
        };
    </script>
</body>
</html>
